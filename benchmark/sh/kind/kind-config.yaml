kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
nodes:
- role: control-plane
  kubeadmConfigPatches:
  - |
    kind: ClusterConfiguration
    apiServer:
        extraArgs:
          max-mutating-requests-inflight: "3000"
          max-requests-inflight: "3000"
    controllerManager:
        extraArgs:
          node-cidr-mask-size: "21"
          kube-api-burst: "3000"
          kube-api-qps: "3000"
    scheduler:
        extraArgs:
          kube-api-burst: "10000"
          kube-api-qps: "10000"
