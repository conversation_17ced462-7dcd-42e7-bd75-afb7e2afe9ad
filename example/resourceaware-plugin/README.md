# ResourceAware Plugin 示例

这是一个自定义的Volcano调度器插件示例，展示了如何创建一个基于资源使用率的调度插件。

## 插件功能

ResourceAware插件提供以下功能：

1. **资源使用率检查**: 过滤掉资源使用率过高的节点
2. **节点评分**: 根据节点的资源使用率对节点进行评分，使用率越低分数越高
3. **抢占控制**: 只允许抢占优先级较低的任务
4. **可配置阈值**: 支持配置CPU和内存的使用率阈值

## 配置参数

| 参数 | 描述 | 默认值 | 范围 |
|------|------|--------|------|
| `cpu.threshold` | CPU使用率阈值 | 0.8 | 0.0-1.0 |
| `memory.threshold` | 内存使用率阈值 | 0.8 | 0.0-1.0 |
| `weight` | 节点评分权重 | 1.0 | > 0 |

## 使用方法

### 1. 编译插件

```bash
# 在volcano项目根目录下执行
make images
```

### 2. 更新调度器配置

使用提供的配置文件更新volcano-scheduler-configmap：

```bash
kubectl apply -f example/resourceaware-plugin/scheduler-config.yaml
```

或者手动编辑现有的ConfigMap：

```bash
kubectl edit cm volcano-scheduler-configmap -n volcano-system
```

### 3. 重启调度器

```bash
kubectl rollout restart deployment volcano-scheduler -n volcano-system
```

## 工作原理

### 预选阶段 (Predicate)
插件会检查节点的CPU和内存使用率，如果超过配置的阈值，该节点将被过滤掉。

### 优选阶段 (NodeOrder)
插件会根据节点的平均资源使用率计算评分：
- 使用率越低，评分越高
- 评分公式：`score = (1.0 - avgUtilization) * 100.0 * weight`

### 抢占阶段 (Preemptable)
插件只允许抢占优先级较低的任务，保护高优先级任务不被抢占。

## 示例配置

```yaml
- name: resourceaware
  arguments:
    cpu.threshold: "0.7"      # 70% CPU阈值
    memory.threshold: "0.9"   # 90% 内存阈值
    weight: "2.0"             # 2倍权重
```

## 测试

运行单元测试：

```bash
cd pkg/scheduler/plugins/resourceaware
go test -v
```

## 扩展

这个插件可以作为基础，进一步扩展以支持：

1. **更多资源类型**: GPU、存储等
2. **历史数据**: 基于历史使用率趋势进行调度
3. **动态阈值**: 根据集群负载动态调整阈值
4. **节点分组**: 对不同类型的节点使用不同的策略
5. **SLA保证**: 基于服务等级协议进行调度决策

## 注意事项

1. 插件的资源使用率计算基于当前的资源分配情况，不是实时使用率
2. 在高负载集群中，过低的阈值可能导致调度失败
3. 权重设置需要与其他插件协调，避免评分冲突
4. 建议在测试环境中充分验证后再部署到生产环境
