## 开源软件供应链点亮计划-暑期2020 

### 开源软件供应链点亮计划-暑期2020背景介绍

“开源软件供应链点亮计划-暑期2020”（以下简称 暑期2020）是由中科院软件所与 openEuler 社区共同举办的一项面向高校学生的暑期活动，旨在鼓励在校学生积极参与开源软件的开发维护，促进国内优秀开源软件社区的蓬勃发展。

该计划将联合各大开源社区，针对重要开源软件的开发与维护提供 mini 项目，并向全国高校学生开放报名。学生可自主选择感兴趣的项目进行申请，并在中选后获得该软件资深维护者（社区导师）亲自指导的机会。根据项目的难易程度和完成情况，参与者还可获取“开源软件供应链点亮计划-暑期2020”活动奖金和奖杯。

**“暑期2020”项目在今年（2020）首次举办，与Google Summer of Code类似，不同点是“暑期2020”只允许中国学生参加，可以看做中国版的GSoC。**

1. 官网：https://isrc.iscas.ac.cn/summer2020
2. 官方新闻：http://www.iscas.ac.cn/xshd2016/xshy2016/202004/t20200426_5563484.html


## Volcano开源项目介绍

Volcano是基于Kubernetes构建的一个云原生批量计算系统，它弥补了Kubernetes在“高性能应用”方面的不足，支持TensorFlow、Spark、MindSpore等多个领域框架，帮助用户通过Kubernetes构建统一的容器平台。Volcano利用CRD抽象出通用的作业API，提供了批量作业的高级生命周期管理能力，并实现了面向批处理高性能计算场景的高级调度能力，包含成组调度，优先级抢占，资源预留，任务拓扑感知，公平调度等多种调度策略。

具体请了解Volcano官网：https://volcano.sh/

## 项目列表

### 1. [Arm架构支持](https://github.com/volcano-sh/volcano/issues/816)
### 2. [官方文档](https://github.com/volcano-sh/volcano/issues/817)
### 3. [Volcano DAG引擎开发](https://github.com/volcano-sh/volcano/issues/818)
### 4. [Volcano工作流可视化UI开发](https://github.com/volcano-sh/volcano/issues/819)


## 候选人要求

### 工作职责：

- 每周与项目导师进行线上讨论，完成项目规定的开发任务。项目导师由开源项目创始人或其他核心成员担任；
- 积极参与开源社区的建设，参与代码提交、解决Issue、审核PR等日常工作；
- 配合完成官方要求的材料提交等事项，包括项目申请书撰写、社区反馈任务完成度追踪等。

### 职位要求：

- 本科、硕士或博士在读（已毕业、工作的无法参加）；
- 对开源软件、开源社区感兴趣；
- 熟悉一种或多种编程语言，有较强的工程能力，代码格式清晰规范，善于团队协作；
- 有一定英文读写能力，能够熟练运用英语在GitHub进行开发、协作；
- 较强的沟通能力和逻辑表达能力。

### 具有以下条件者优先：

- 熟悉Docker、Kubernetes等云原生技术；
- 熟悉Go开发，有相关项目经验；
- 熟悉前端可视化开发；
- 在GitHub较为活跃，有自己的开源项目，或参与过知名开源项目；
- 可以在项目结束后继续长期参与开源社区的开发、建设或维护。


## 投递要求

### 1. 与项目导师联系

### 2. 官网投递（2020年6月1日至6月20日）

详见：https://isrc.iscas.ac.cn/summer2020/help/student.html#学生如何报名