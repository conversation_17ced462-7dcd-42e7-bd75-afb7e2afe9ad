# Adopters of Volcano

Below are the adopters of project Volcano. If you are using Volcano to improve scheduling or job management in AI/ML, big-data, etc,
please feel free to add yourself into the following list by a pull request. There're several phases as follow:

* **Evaluation:** Known Volcano, that's interesting; evaluating the features/scopes of Volcano
* **Testing:** Take Volcano as one of candidates, testing Kubernetes cluster with Volcano
* **Staging:** Decide to use Volcano, testing it in pre-product environment
* **Production:** Already put Volcano into product environment

| Organization | Contact | Environment | Description of Use |
| ------------ | ------- | ----------- | ------------------ |
| [Huawei Cloud](https://www.huaweicloud.com/intl/en-us/) |[@k82cn](https://github.com/k82cn)| Production | Scheduler & Job Management of AI Container Service and [CCI](https://www.huaweicloud.com/product/cci.html) service |
| [Tencent](https://www.tencent.com/zh-cn) |[@carmark](https://github.com/carmark)| Production | Production in AI Platform |
| [Baidu](https://baidu.com/) |[@tizhou86](https://github.com/tizhou86)| Production | Scheduler for Deep Learning Platform to Optimize Performance |
| [IQIYI](https://www.iqiyi.com/) | [@lihao](https://github.com/silenceli)| Production | Scheduler & Job Management for Deep Learning platform on K8s |
| [Xiaohongshu](https://www.xiaohongshu.com/) | [@xiaogaozi](https://github.com/xiaogaozi), [@Cplo](https://github.com/Cplo) | Production | Batch system for internal machine learning platform |
| [VIPS](https://www.vip.com/) | [@xinchun-wang](https://github.com/xinchun-wang) | Production | Tensorflow on K8s |
| [DiDi](https://www.didiglobal.com/) | [@tongchao199](https://github.com/tongchao199) | Production | Batch system for Distributed Deep Learning platform on K8s |
| [Bosszhipin](https://www.zhipin.com/?sid=sem_pz_bdpc_dasou_title) | [@hudson741](https://github.com/hudson741) | Production | Batch system for Distributed AI platform on K8s |
| [Leinao](http://www.leinao.ai/) | [@king-jingxiang](https://github.com/king-jingxiang) | Production | Work as part of Leinao OS |
| [Ruitian Capital](https://www.ruitiancapital.com/#/) | [@zen-xu](https://github.com/zen-xu) | Production | Batch system for AI platform on K8s |
| [JD Retail Infrastructure Department](https://jd.com/) |[@yuanchen8911](https://github.com/yuanchen8911)| Evaluation | Spark on K8S  |
| [BIBDR](http://www.bibdr.org/en/) |[@felix5572](https://github.com/felix5572)| Evaluation | Scientific calculations in physics, materials , biology and chemistry. molecular dynamics simulation. |
| [Unisound](https://www.unisound.com/) |[@xieydd](https://github.com/xieydd)| Evaluation | Evaluation in ATLAS AI Platform |
| [Shareit](https://www.ushareit.com/) | [@yujiantao](https://github.com/yujiantao) | Evaluation | Spark on K8s |
| [Vivo](https://www.vivo.com/) | [@zionwu](https://github.com/zionwu) | Evaluation | Scheduler for Vtraining (deep learning training platform) |
| [GrandOmics](https://www.grandomics.com/) |[@alartin](https://github.com/alartin)| Evaluation | Infrastructure of Hanwell (Huawei Cloud backend of Cromwell which is a Broad Institute implementation of WDL) |
| [kt NexR](https://www.ktnexr.com) |[@minyk](https://github.com/minyk), [@dieselnexr](https://github.com/dieselnexr)| Evaluation | spark scheduler of our next cloud native product. |
| [QTT Bigdata Infra](https://ir.qutoutiao.net/) |[@yuzhaojing](https://github.com/yuzhaojing) | Evaluation | Spark and AI on K8S. |
| [Predibase](https://predibase.com/) |[@tgaddair](https://github.com/tgaddair) | Staging | Ray on K8s. |
