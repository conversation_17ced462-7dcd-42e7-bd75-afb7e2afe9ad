# Open Source Promotion Plan - Summer 2020

## Background

This is a program hosted by ISCAS(Institute of Software Chinese Academy of Sciences) and openEuler community. It is very similar with Google Summer of Code, the only difference is that it is only targeted for Chinese students.

This year(2020) is their first time to host this event.

- Official website: https://isrc.iscas.ac.cn/summer2020

## Brief introduction of volcano

Volcano is a kubernetes native batch system, it aims to cover the shortage of kubernetes on batch service. And it has supported Tensorflow, MPI, Spark, MindSpore and so many other frameworks covering many areas, such as AI, BigData, HPC and Genomic.
Volcano makes use of CRD to declare a common API for batch job, and provides the capability on advanced job lifecycle management.
It also provides advanced scheduling capability intended for batch and HPC scenarios, including gang-scheduling, fairshare, drf, binpack, preempt, topology-aware, etc.

More details please refer to [volcano website](https://volcano.sh/)

## Tasks

As this program is designed for Chinese students, all proposed tasks are listed [here](./summer2020-cn.md#项目列表) in Chinese.
