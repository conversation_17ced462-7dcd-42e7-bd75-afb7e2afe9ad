<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="379px" height="482px" version="1.1" content="&lt;mxfile userAgent=&quot;Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.150 Safari/537.36&quot; version=&quot;8.9.6&quot; editor=&quot;www.draw.io&quot;&gt;&lt;diagram id=&quot;1fa4ae10-0e58-52ba-0bd6-27a232dec292&quot; name=&quot;第 1 页&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;"><defs><style type="text/css">div.INSTANCEICON:after{width:8px;height:14px;font-size:12px;line-height:14px;display:inline-block;text-align:center;color:#000;content:":";font-weight:700;}div.LINKICON:after,span.PACKAGEICON:after{width:8px;height:16px;font-size:12px;line-height:14px;display:inline-block;text-align:center;color:#000;content:"::";position:absolute;font-weight:700;}</style></defs><g transform="translate(0.5,0.5)"><path d="M 77 121 L 137 156 L 77 191 L 17 156 Z" fill="#ffffff" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(32.5,142.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="87" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 87px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Enqueue<br />jobEnqueueable<br /></div></div></foreignObject><text x="44" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">Enqueue&lt;br&gt;jobEnqueueable&lt;br&gt;</text></switch></g><rect x="27" y="61" width="100" height="40" rx="2.4" ry="2.4" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="none"/><g transform="translate(45.5,67.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="61" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 61px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">enqueue<br />jobOrderFn</div></div></foreignObject><text x="31" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">enqueue&lt;br&gt;jobOrderFn</text></switch></g><path d="M 77 101 L 77 114.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 77 119.88 L 73.5 112.88 L 77 114.63 L 80.5 112.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 217 120 L 277 155 L 217 190 L 157 155 Z" fill="#ffffff" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(175.5,141.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="81" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 83px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">jobWaitTime &gt;<br />slaWaitingTime</div></div></foreignObject><text x="41" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 137 156 L 150.63 155" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 155.88 155 L 148.88 158.5 L 150.63 155 L 148.88 151.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(139.5,140.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="9" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;background-color:transparent;">N</div></div></foreignObject><text x="5" y="12" fill="#000000" text-anchor="middle" font-size="11px" font-family="Helvetica">N</text></switch></g><rect x="297" y="287" width="80" height="40" rx="2.4" ry="2.4" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="none"/><g transform="translate(304.5,300.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="63" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 65px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">job Pending</div></div></foreignObject><text x="32" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">job Pending</text></switch></g><path d="M 277 155 L 327 155 Q 337 155 337 165 L 337 280.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 337 285.88 L 333.5 278.88 L 337 280.63 L 340.5 278.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(322.5,175.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="9" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;background-color:transparent;">N</div></div></foreignObject><text x="5" y="12" fill="#000000" text-anchor="middle" font-size="11px" font-family="Helvetica">N</text></switch></g><path d="M 337 327 L 337 434.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 337 439.88 L 333.5 432.88 L 337 434.63 L 340.5 432.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 321.49 441 L 352.51 441 C 366.04 441 377 449.95 377 461 C 377 472.05 366.04 481 352.51 481 L 321.49 481 C 307.96 481 297 472.05 297 461 C 297 449.95 307.96 441 321.49 441 Z" fill="#ffffff" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(299.5,454.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="73" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 75px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">CloseSession</div></div></foreignObject><text x="37" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">CloseSession</text></switch></g><ellipse cx="77" cy="21" rx="50" ry="20" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="none"/><g transform="translate(39.5,14.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="73" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 73px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">OpenSession</div></div></foreignObject><text x="37" y="12" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">OpenSession</text></switch></g><path d="M 77 41 L 77 54.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 77 59.88 L 73.5 52.88 L 77 54.63 L 80.5 52.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 77 191 L 77 214.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 77 219.88 L 73.5 212.88 L 77 214.63 L 80.5 212.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(62.5,190.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="8" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;background-color:transparent;">Y</div></div></foreignObject><text x="4" y="12" fill="#000000" text-anchor="middle" font-size="11px" font-family="Helvetica">Y</text></switch></g><rect x="27" y="221" width="100" height="40" rx="2.4" ry="2.4" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="none"/><g transform="translate(45.5,227.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="61" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 61px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">allocate<br />jobOrderFn</div></div></foreignObject><text x="31" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">allocate&lt;br&gt;jobOrderFn</text></switch></g><path d="M 217 191 L 217 198.5 Q 217 206 207 206 L 87 206 Q 77 206 80.18 206 L 83.37 206" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 78.12 206 L 85.12 202.5 L 83.37 206 L 85.12 209.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(152.5,190.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="8" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;background-color:transparent;">Y</div></div></foreignObject><text x="4" y="12" fill="#000000" text-anchor="middle" font-size="11px" font-family="Helvetica">Y</text></switch></g><path d="M 77 281 L 127 306 L 77 331 L 27 306 Z" fill="#ffffff" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(50.5,292.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="51" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 51px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">Allocate<br />jobReady<br /></div></div></foreignObject><text x="26" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 77 261 L 77 274.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 77 279.88 L 73.5 272.88 L 77 274.63 L 80.5 272.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 217 272 L 277 307 L 217 342 L 157 307 Z" fill="#ffffff" stroke="#000000" stroke-width="2" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(175.5,293.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="81" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 83px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">jobWaitTime &gt;<br />slaWaitingTime</div></div></foreignObject><text x="41" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">[Not supported by viewer]</text></switch></g><path d="M 127 306 L 150.64 306.79" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 155.88 306.96 L 148.77 310.23 L 150.64 306.79 L 149 303.23 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(131.5,290.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="9" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;background-color:transparent;">N</div></div></foreignObject><text x="5" y="12" fill="#000000" text-anchor="middle" font-size="11px" font-family="Helvetica">N</text></switch></g><path d="M 277 307 L 290.63 307" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 295.88 307 L 288.88 310.5 L 290.63 307 L 288.88 303.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(279.5,291.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="9" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;background-color:transparent;">N</div></div></foreignObject><text x="5" y="12" fill="#000000" text-anchor="middle" font-size="11px" font-family="Helvetica">N</text></switch></g><rect x="157" y="361" width="120" height="40" rx="2.4" ry="2.4" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="none"/><g transform="translate(167.5,367.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="97" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 97px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">job Pipelined<br />resource reserved<br /></div></div></foreignObject><text x="49" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">job Pipelined&lt;br&gt;resource reserved&lt;br&gt;</text></switch></g><path d="M 217 342 L 217 354.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 217 359.88 L 213.5 352.88 L 217 354.63 L 220.5 352.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(202.5,345.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="8" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;background-color:transparent;">Y</div></div></foreignObject><text x="4" y="12" fill="#000000" text-anchor="middle" font-size="11px" font-family="Helvetica">Y</text></switch></g><path d="M 217 401 L 217 411 Q 217 421 227 421 L 327 421 Q 337 421 337 427.82 L 337 434.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 337 439.88 L 333.5 432.88 L 337 434.63 L 340.5 432.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><rect x="37" y="361" width="80" height="40" rx="2.4" ry="2.4" fill="#ffffff" stroke="#000000" stroke-width="2" pointer-events="none"/><g transform="translate(42.5,367.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="67" height="26" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; width: 69px; white-space: nowrap; overflow-wrap: normal; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;">job Commit /<br />Pipelined</div></div></foreignObject><text x="34" y="19" fill="#000000" text-anchor="middle" font-size="12px" font-family="Helvetica">job Commit /&lt;br&gt;Pipelined</text></switch></g><path d="M 77 331 L 77 354.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 77 359.88 L 73.5 352.88 L 77 354.63 L 80.5 352.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><g transform="translate(62.5,334.5)"><switch><foreignObject style="overflow:visible;" pointer-events="all" width="8" height="12" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"><div xmlns="http://www.w3.org/1999/xhtml" style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; vertical-align: top; white-space: nowrap; text-align: center;"><div xmlns="http://www.w3.org/1999/xhtml" style="display:inline-block;text-align:inherit;text-decoration:inherit;background-color:transparent;">Y</div></div></foreignObject><text x="4" y="12" fill="#000000" text-anchor="middle" font-size="11px" font-family="Helvetica">Y</text></switch></g><path d="M 77 401 L 77 411 Q 77 421 87 421 L 327 421 Q 337 421 337 427.82 L 337 434.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 337 439.88 L 333.5 432.88 L 337 434.63 L 340.5 432.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 297 461 L 17 461 Q 7 461 7 451 L 7 31 Q 7 21 13.82 21 L 20.63 21" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/><path d="M 25.88 21 L 18.88 24.5 L 20.63 21 L 18.88 17.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="none"/></g></svg>