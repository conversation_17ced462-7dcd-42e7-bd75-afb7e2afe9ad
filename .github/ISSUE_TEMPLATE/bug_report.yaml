name: Bug report
description: Create a bug report to help improve volcano
labels: kind/bug
body:
  - type: markdown
    attributes:
      value: |
        If you are reporting a new issue, make sure that we do not have any duplicates
        already open. You can ensure this by searching the issue list for this
        repository. If there is a duplicate, please close your issue and add a comment
        to the existing issue instead.

        If you're looking for a help then check our [Slack Channel](https://cloud-native.slack.com/messages/volcano) or have a look at our [dev mailing](https://groups.google.com/forum/#!forum/volcano-sh) 

  - type: textarea
    attributes:
      label: Description
      description: |
        Briefly describe the problem you are having in a few paragraphs.
    validations:
      required: true

  - type: textarea
    attributes:
      label: Steps to reproduce the issue
      value: |
        1.
        2.
        3.

  - type: textarea
    attributes:
      label: Describe the results you received and expected
    validations:
      required: true

  - type: input
    attributes:
      label: What version of Volcano are you using?
      placeholder: Volcano image tag
    validations:
      required: true

  - type: textarea
    attributes:
      label: Any other relevant information
      description: |
        Kubernetes version, OS configuration, OS/Kernel version, etc.
        Use the following commands:
        $ kubectl version
        $ cat /etc/os-release
        $ uname -a
