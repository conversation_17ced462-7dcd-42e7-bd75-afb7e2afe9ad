name: Code Verify

on:
  push:
    branches:
      - master
    tags:
  pull_request:

jobs:
  verify:
    runs-on: ubuntu-24.04
    name: Verify codes, generated files
    timeout-minutes: 40
    env:
      GOPATH: /home/<USER>/work/${{ github.repository }}
    steps:
      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23.x

      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          path: ./src/github.com/${{ github.repository }}

      - name: Set TAG from release config
        run: |
          cd ./src/github.com/${{ github.repository }}
          if [[ -f .release-version ]]; then
            TAG=$(cat .release-version | tr -d '\n\r')
            echo "TAG=${TAG}" >> $GITHUB_ENV
            echo "Using TAG from .release-version: ${TAG}"
          else
            echo "TAG=latest" >> $GITHUB_ENV
            echo "Using default TAG=latest (.release-version file not found)"
          fi

      - uses: actions/cache@v4
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}

      - name: Run verify test
        run: |
          make lint
          make verify
          make TAG=${{ env.TAG }} generate-yaml
          make TAG=${{ env.TAG }} verify-generated-yaml
          sudo make unit-test
        working-directory: ./src/github.com/${{ github.repository }}
