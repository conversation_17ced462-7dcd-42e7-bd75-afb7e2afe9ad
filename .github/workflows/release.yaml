name: daily release
on:
  schedule:
  # Runs at 0:00 and 12:00 UTC every day
  - cron:  '0 */12 * * *'
  # Automatically push the image when releasing the version
  push:
    tags:
      - "v*.*.*"


jobs:
  docker:
    runs-on: ubuntu-latest
    env:
      GOPATH: /home/<USER>/work/${{ github.repository }}
    steps:
      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23.x

      - name: Install musl
        run: |
          wget http://musl.libc.org/releases/musl-1.2.1.tar.gz
          tar -xf musl-1.2.1.tar.gz && cd musl-1.2.1
          ./configure
          make && sudo make install

      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          path: ./src/github.com/${{ github.repository }}

      - name: Get tag or branch name
        run: |
          echo "TAG=${{ github.ref_name }}" >> $GITHUB_ENV
          echo "RELEASE_VER=${{ github.ref_name }}" >> $GITHUB_ENV

      - name: Update tag and release_ver
        if: ${{ github.ref_name == 'master' }}
        run: |
          echo "TAG=latest" >> $GITHUB_ENV
          echo "RELEASE_VER=latest" >> $GITHUB_ENV

      - uses: actions/cache@v4
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}

      - name: Run verify test
        run: |
          make verify
          make TAG=${{ env.TAG }} generate-yaml
          make TAG=${{ env.TAG }} verify-generated-yaml
          sudo make unit-test
        working-directory: ./src/github.com/${{ github.repository }}

      - name: Login to DockerHub
        uses: docker/login-action@v3 
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Daily Release
        run: docker buildx create --use && make TAG=${{ env.TAG }} RELEASE_VER=${{ env.RELEASE_VER }} DOCKER_USERNAME=${{ secrets.DOCKERHUB_USERNAME }} DOCKER_PASSWORD=${{ secrets.DOCKERHUB_TOKEN }} CC=/usr/local/musl/bin/musl-gcc DOCKER_PLATFORMS="linux/amd64,linux/arm64" BUILDX_OUTPUT_TYPE=registry release
        working-directory: ./src/github.com/${{ github.repository }}

      - name: Loginout DockerHub
        run: docker logout
