name: Approve Workflows

on:
  pull_request_target:
    types:
      - labeled
      - synchronize
    branches:
      - master
      - release-**

permissions:
  contents: read

jobs:
  approve:
    name: Approve workflows based on contributor status
    runs-on: ubuntu-latest
    permissions:
      actions: write
    steps:
      - name: Check if first-time contributor
        id: check_first_time
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const contributor = context.payload.pull_request.user.login;
            const repo = context.repo.repo;
            const owner = context.repo.owner;

            const { data: prs } = await github.rest.pulls.list({
              owner,
              repo,
              state: 'all',
              per_page: 100
            });

            const isFirstTime = prs.filter(pr => pr.user.login === contributor).length <= 1;
            core.setOutput('is_first_time', isFirstTime);

      - name: Execute workflows if not first-time contributor or has ok-to-test label
        if: steps.check_first_time.outputs.is_first_time == 'false' || contains(github.event.pull_request.labels.*.name, 'ok-to-test')
        uses: actions/github-script@v7
        continue-on-error: true
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const result = await github.rest.actions.listWorkflowRunsForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              event: "pull_request",
              status: "action_required",
              head_sha: context.payload.pull_request.head.sha,
              per_page: 100
            });

            for (var run of result.data.workflow_runs) {
              await github.rest.actions.approveWorkflowRun({
                owner: context.repo.owner,
                repo: context.repo.repo,
                run_id: run.id
              });
            }
