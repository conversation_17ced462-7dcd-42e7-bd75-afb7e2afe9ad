name: Scheduling Actions

on:
  push:
    branches:
      - master
    tags:
  pull_request:

jobs:
  e2e_scheduling_actions:
    runs-on: ubuntu-24.04
    name: E2E about Scheduling Actions
    timeout-minutes: 50
    steps:
      - name: Install Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23.x

      - name: Install musl
        run: |
          wget http://musl.libc.org/releases/musl-1.2.1.tar.gz
          tar -xf musl-1.2.1.tar.gz && cd musl-1.2.1
          ./configure
          make && sudo make install
      - uses: actions/cache@v4
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}

      - name: Install dependences
        run: |
          GO111MODULE="on" go install sigs.k8s.io/kind@v0.26.0
          curl -LO https://dl.k8s.io/release/v1.32.0/bin/linux/amd64/kubectl && sudo install kubectl /usr/local/bin/kubectl
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Run E2E Tests
        run: |
          export ARTIFACTS_PATH=${{ github.workspace }}/e2e-scheduling-actions-logs
          make e2e-test-schedulingaction CC=/usr/local/musl/bin/musl-gcc

      - name: Upload e2e scheduling actions logs
        if: failure()
        uses: actions/upload-artifact@v4
        with:
          name: volcano_e2e_scheduling_actions_logs
          path: ${{ github.workspace }}/e2e-scheduling-actions-logs
