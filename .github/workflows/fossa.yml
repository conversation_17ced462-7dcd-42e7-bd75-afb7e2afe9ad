name: FOSSA
on:
  push:
    branches: [master]
  pull_request:
    branches: [master]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v5
        with:
          go-version: 1.23.x
      - run: go version
      # Runs a set of commands to initialize and analyze with FOSSA
      - name: run FOSSA analysis
        env:
          # FOSSA Push-Only API Token
          FOSSA_API_KEY: '1a92b8d8ee4304c0ad85a726b9e9acab'
          BINDIR: /home/<USER>/work/volcano/volcano
        run: |
          export GOPATH=$HOME/go
          export PATH=$PATH:$(go env GOPATH)/bin
          curl -H 'Cache-Control: no-cache' https://raw.githubusercontent.com/fossas/fossa-cli/master/install.sh | bash -s -- -b /home/<USER>/work/volcano/volcano 
          /home/<USER>/work/volcano/volcano/fossa init
          /home/<USER>/work/volcano/volcano/fossa analyze
