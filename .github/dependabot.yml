version: 2
updates:
  - package-ecosystem: gomod
    directory: /
    schedule:
      interval: weekly
    groups:
      k8s-libs:
        patterns:
          - "k8s.io/api"
          - "k8s.io/apiextensions-apiserver"
          - "k8s.io/apimachinery"
          - "k8s.io/apiserver"
          - "k8s.io/cli-runtime"
          - "k8s.io/client-go"
          - "k8s.io/cloud-provider"
          - "k8s.io/cluster-bootstrap"
          - "k8s.io/code-generator"
          - "k8s.io/component-base"
          - "k8s.io/component-helpers"
          - "k8s.io/controller-manager"
          - "k8s.io/cri-api"
          - "k8s.io/cri-client"
          - "k8s.io/csi-translation-lib"
          - "k8s.io/dynamic-resource-allocation"
          - "k8s.io/endpointslice"
          - "k8s.io/kube-aggregator"
          - "k8s.io/kube-controller-manager"
          - "k8s.io/kube-proxy"
          - "k8s.io/kube-scheduler"
          - "k8s.io/kubectl"
          - "k8s.io/kubelet"
          - "k8s.io/legacy-cloud-providers"
          - "k8s.io/metrics"
          - "k8s.io/mount-utils"
          - "k8s.io/node-api"
          - "k8s.io/pod-security-admission"
          - "k8s.io/sample-apiserver"
          - "k8s.io/sample-cli-plugin"
          - "k8s.io/sample-controller"

  - package-ecosystem: github-actions
    directory: /
    schedule:
      interval: weekly
