
---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.0
  creationTimestamp: null
  name: jobs.batch.volcano.sh
spec:
  additionalPrinterColumns:
  - JSONPath: .status.state.phase
    name: STATUS
    type: string
  - JSONPath: .status.minAvailable
    name: minAvailable
    type: integer
  - JSONPath: .status.running
    name: RUNNINGS
    type: integer
  - JSONPath: .metadata.creationTimestamp
    name: AGE
    type: date
  - JSONPath: .spec.queue
    name: QUEUE
    priority: 1
    type: string
  group: batch.volcano.sh
  names:
    kind: Job
    listKind: JobList
    plural: jobs
    shortNames:
    - vcjob
    - vj
    singular: job
  scope: Namespaced
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      properties:
        apiVersion:
          type: string
        kind:
          type: string
        metadata:
          type: object
        spec:
          properties:
            maxRetry:
              format: int32
              type: integer
            minAvailable:
              format: int32
              type: integer
            minSuccess:
              format: int32
              minimum: 1
              type: integer
            plugins:
              additionalProperties:
                items:
                  type: string
                type: array
              type: object
            policies:
              items:
                properties:
                  action:
                    type: string
                  event:
                    type: string
                  events:
                    items:
                      type: string
                    type: array
                  exitCode:
                    format: int32
                    type: integer
                  timeout:
                    type: string
                type: object
              type: array
            priorityClassName:
              type: string
            queue:
              type: string
            runningEstimate:
              type: string
            schedulerName:
              type: string
            tasks:
              items:
                properties:
                  dependsOn:
                    properties:
                      iteration:
                        type: string
                      name:
                        items:
                          type: string
                        type: array
                    type: object
                  maxRetry:
                    format: int32
                    type: integer
                  minAvailable:
                    format: int32
                    type: integer
                  name:
                    type: string
                  policies:
                    items:
                      properties:
                        action:
                          type: string
                        event:
                          type: string
                        events:
                          items:
                            type: string
                          type: array
                        exitCode:
                          format: int32
                          type: integer
                        timeout:
                          type: string
                      type: object
                    type: array
                  replicas:
                    format: int32
                    type: integer
                  template:
                    properties:
                      metadata:
                        type: object
                      spec:
                        properties:
                          activeDeadlineSeconds:
                            format: int64
                            type: integer
                          affinity:
                            properties:
                              nodeAffinity:
                                properties:
                                  preferredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        preference:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                            matchFields:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                          type: object
                                        weight:
                                          format: int32
                                          type: integer
                                      required:
                                      - preference
                                      - weight
                                      type: object
                                    type: array
                                  requiredDuringSchedulingIgnoredDuringExecution:
                                    properties:
                                      nodeSelectorTerms:
                                        items:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                            matchFields:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                          type: object
                                        type: array
                                    required:
                                    - nodeSelectorTerms
                                    type: object
                                type: object
                              podAffinity:
                                properties:
                                  preferredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        podAffinityTerm:
                                          properties:
                                            labelSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    required:
                                                    - key
                                                    - operator
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                            matchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                              x-kubernetes-list-type: atomic
                                            mismatchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                              x-kubernetes-list-type: atomic
                                            namespaceSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    required:
                                                    - key
                                                    - operator
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                            namespaces:
                                              items:
                                                type: string
                                              type: array
                                            topologyKey:
                                              type: string
                                          required:
                                          - topologyKey
                                          type: object
                                        weight:
                                          format: int32
                                          type: integer
                                      required:
                                      - podAffinityTerm
                                      - weight
                                      type: object
                                    type: array
                                  requiredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        labelSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                        matchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        mismatchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        namespaceSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                        namespaces:
                                          items:
                                            type: string
                                          type: array
                                        topologyKey:
                                          type: string
                                      required:
                                      - topologyKey
                                      type: object
                                    type: array
                                type: object
                              podAntiAffinity:
                                properties:
                                  preferredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        podAffinityTerm:
                                          properties:
                                            labelSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    required:
                                                    - key
                                                    - operator
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                            matchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                              x-kubernetes-list-type: atomic
                                            mismatchLabelKeys:
                                              items:
                                                type: string
                                              type: array
                                              x-kubernetes-list-type: atomic
                                            namespaceSelector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    required:
                                                    - key
                                                    - operator
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                            namespaces:
                                              items:
                                                type: string
                                              type: array
                                            topologyKey:
                                              type: string
                                          required:
                                          - topologyKey
                                          type: object
                                        weight:
                                          format: int32
                                          type: integer
                                      required:
                                      - podAffinityTerm
                                      - weight
                                      type: object
                                    type: array
                                  requiredDuringSchedulingIgnoredDuringExecution:
                                    items:
                                      properties:
                                        labelSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                        matchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        mismatchLabelKeys:
                                          items:
                                            type: string
                                          type: array
                                          x-kubernetes-list-type: atomic
                                        namespaceSelector:
                                          properties:
                                            matchExpressions:
                                              items:
                                                properties:
                                                  key:
                                                    type: string
                                                  operator:
                                                    type: string
                                                  values:
                                                    items:
                                                      type: string
                                                    type: array
                                                required:
                                                - key
                                                - operator
                                                type: object
                                              type: array
                                            matchLabels:
                                              additionalProperties:
                                                type: string
                                              type: object
                                          type: object
                                        namespaces:
                                          items:
                                            type: string
                                          type: array
                                        topologyKey:
                                          type: string
                                      required:
                                      - topologyKey
                                      type: object
                                    type: array
                                type: object
                            type: object
                          automountServiceAccountToken:
                            type: boolean
                          containers:
                            items:
                              properties:
                                args:
                                  items:
                                    type: string
                                  type: array
                                command:
                                  items:
                                    type: string
                                  type: array
                                env:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                      valueFrom:
                                        properties:
                                          configMapKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            required:
                                            - key
                                            type: object
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            required:
                                            - fieldPath
                                            type: object
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            required:
                                            - resource
                                            type: object
                                          secretKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            required:
                                            - key
                                            type: object
                                        type: object
                                    required:
                                    - name
                                    type: object
                                  type: array
                                envFrom:
                                  items:
                                    properties:
                                      configMapRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                      prefix:
                                        type: string
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                    type: object
                                  type: array
                                image:
                                  type: string
                                imagePullPolicy:
                                  type: string
                                lifecycle:
                                  properties:
                                    postStart:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                required:
                                                - name
                                                - value
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          required:
                                          - port
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              format: int64
                                              type: integer
                                          required:
                                          - seconds
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          required:
                                          - port
                                          type: object
                                      type: object
                                    preStop:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                required:
                                                - name
                                                - value
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          required:
                                          - port
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              format: int64
                                              type: integer
                                          required:
                                          - seconds
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          required:
                                          - port
                                          type: object
                                      type: object
                                  type: object
                                livenessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                name:
                                  type: string
                                ports:
                                  items:
                                    properties:
                                      containerPort:
                                        format: int32
                                        type: integer
                                      hostIP:
                                        type: string
                                      hostPort:
                                        format: int32
                                        type: integer
                                      name:
                                        type: string
                                      protocol:
                                        type: string
                                    required:
                                    - containerPort
                                    type: object
                                  type: array
                                  x-kubernetes-list-map-keys:
                                  - containerPort
                                  - protocol
                                  x-kubernetes-list-type: map
                                readinessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                resizePolicy:
                                  items:
                                    properties:
                                      resourceName:
                                        type: string
                                      restartPolicy:
                                        type: string
                                    required:
                                    - resourceName
                                    - restartPolicy
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                resources:
                                  properties:
                                    claims:
                                      items:
                                        properties:
                                          name:
                                            type: string
                                        required:
                                        - name
                                        type: object
                                      type: array
                                      x-kubernetes-list-map-keys:
                                      - name
                                      x-kubernetes-list-type: map
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                  type: object
                                restartPolicy:
                                  type: string
                                securityContext:
                                  properties:
                                    allowPrivilegeEscalation:
                                      type: boolean
                                    capabilities:
                                      properties:
                                        add:
                                          items:
                                            type: string
                                          type: array
                                        drop:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    privileged:
                                      type: boolean
                                    procMount:
                                      type: string
                                    readOnlyRootFilesystem:
                                      type: boolean
                                    runAsGroup:
                                      format: int64
                                      type: integer
                                    runAsNonRoot:
                                      type: boolean
                                    runAsUser:
                                      format: int64
                                      type: integer
                                    seLinuxOptions:
                                      properties:
                                        level:
                                          type: string
                                        role:
                                          type: string
                                        type:
                                          type: string
                                        user:
                                          type: string
                                      type: object
                                    seccompProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      required:
                                      - type
                                      type: object
                                    windowsOptions:
                                      properties:
                                        gmsaCredentialSpec:
                                          type: string
                                        gmsaCredentialSpecName:
                                          type: string
                                        hostProcess:
                                          type: boolean
                                        runAsUserName:
                                          type: string
                                      type: object
                                  type: object
                                startupProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                stdin:
                                  type: boolean
                                stdinOnce:
                                  type: boolean
                                terminationMessagePath:
                                  type: string
                                terminationMessagePolicy:
                                  type: string
                                tty:
                                  type: boolean
                                volumeDevices:
                                  items:
                                    properties:
                                      devicePath:
                                        type: string
                                      name:
                                        type: string
                                    required:
                                    - devicePath
                                    - name
                                    type: object
                                  type: array
                                volumeMounts:
                                  items:
                                    properties:
                                      mountPath:
                                        type: string
                                      mountPropagation:
                                        type: string
                                      name:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      subPath:
                                        type: string
                                      subPathExpr:
                                        type: string
                                    required:
                                    - mountPath
                                    - name
                                    type: object
                                  type: array
                                workingDir:
                                  type: string
                              required:
                              - name
                              type: object
                            type: array
                          dnsConfig:
                            properties:
                              nameservers:
                                items:
                                  type: string
                                type: array
                              options:
                                items:
                                  properties:
                                    name:
                                      type: string
                                    value:
                                      type: string
                                  type: object
                                type: array
                              searches:
                                items:
                                  type: string
                                type: array
                            type: object
                          dnsPolicy:
                            type: string
                          enableServiceLinks:
                            type: boolean
                          ephemeralContainers:
                            items:
                              properties:
                                args:
                                  items:
                                    type: string
                                  type: array
                                command:
                                  items:
                                    type: string
                                  type: array
                                env:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                      valueFrom:
                                        properties:
                                          configMapKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            required:
                                            - key
                                            type: object
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            required:
                                            - fieldPath
                                            type: object
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            required:
                                            - resource
                                            type: object
                                          secretKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            required:
                                            - key
                                            type: object
                                        type: object
                                    required:
                                    - name
                                    type: object
                                  type: array
                                envFrom:
                                  items:
                                    properties:
                                      configMapRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                      prefix:
                                        type: string
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                    type: object
                                  type: array
                                image:
                                  type: string
                                imagePullPolicy:
                                  type: string
                                lifecycle:
                                  properties:
                                    postStart:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                required:
                                                - name
                                                - value
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          required:
                                          - port
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              format: int64
                                              type: integer
                                          required:
                                          - seconds
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          required:
                                          - port
                                          type: object
                                      type: object
                                    preStop:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                required:
                                                - name
                                                - value
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          required:
                                          - port
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              format: int64
                                              type: integer
                                          required:
                                          - seconds
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          required:
                                          - port
                                          type: object
                                      type: object
                                  type: object
                                livenessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                name:
                                  type: string
                                ports:
                                  items:
                                    properties:
                                      containerPort:
                                        format: int32
                                        type: integer
                                      hostIP:
                                        type: string
                                      hostPort:
                                        format: int32
                                        type: integer
                                      name:
                                        type: string
                                      protocol:
                                        type: string
                                    required:
                                    - containerPort
                                    type: object
                                  type: array
                                  x-kubernetes-list-map-keys:
                                  - containerPort
                                  - protocol
                                  x-kubernetes-list-type: map
                                readinessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                resizePolicy:
                                  items:
                                    properties:
                                      resourceName:
                                        type: string
                                      restartPolicy:
                                        type: string
                                    required:
                                    - resourceName
                                    - restartPolicy
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                resources:
                                  properties:
                                    claims:
                                      items:
                                        properties:
                                          name:
                                            type: string
                                        required:
                                        - name
                                        type: object
                                      type: array
                                      x-kubernetes-list-map-keys:
                                      - name
                                      x-kubernetes-list-type: map
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                  type: object
                                restartPolicy:
                                  type: string
                                securityContext:
                                  properties:
                                    allowPrivilegeEscalation:
                                      type: boolean
                                    capabilities:
                                      properties:
                                        add:
                                          items:
                                            type: string
                                          type: array
                                        drop:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    privileged:
                                      type: boolean
                                    procMount:
                                      type: string
                                    readOnlyRootFilesystem:
                                      type: boolean
                                    runAsGroup:
                                      format: int64
                                      type: integer
                                    runAsNonRoot:
                                      type: boolean
                                    runAsUser:
                                      format: int64
                                      type: integer
                                    seLinuxOptions:
                                      properties:
                                        level:
                                          type: string
                                        role:
                                          type: string
                                        type:
                                          type: string
                                        user:
                                          type: string
                                      type: object
                                    seccompProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      required:
                                      - type
                                      type: object
                                    windowsOptions:
                                      properties:
                                        gmsaCredentialSpec:
                                          type: string
                                        gmsaCredentialSpecName:
                                          type: string
                                        hostProcess:
                                          type: boolean
                                        runAsUserName:
                                          type: string
                                      type: object
                                  type: object
                                startupProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                stdin:
                                  type: boolean
                                stdinOnce:
                                  type: boolean
                                targetContainerName:
                                  type: string
                                terminationMessagePath:
                                  type: string
                                terminationMessagePolicy:
                                  type: string
                                tty:
                                  type: boolean
                                volumeDevices:
                                  items:
                                    properties:
                                      devicePath:
                                        type: string
                                      name:
                                        type: string
                                    required:
                                    - devicePath
                                    - name
                                    type: object
                                  type: array
                                volumeMounts:
                                  items:
                                    properties:
                                      mountPath:
                                        type: string
                                      mountPropagation:
                                        type: string
                                      name:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      subPath:
                                        type: string
                                      subPathExpr:
                                        type: string
                                    required:
                                    - mountPath
                                    - name
                                    type: object
                                  type: array
                                workingDir:
                                  type: string
                              required:
                              - name
                              type: object
                            type: array
                          hostAliases:
                            items:
                              properties:
                                hostnames:
                                  items:
                                    type: string
                                  type: array
                                ip:
                                  type: string
                              type: object
                            type: array
                          hostIPC:
                            type: boolean
                          hostNetwork:
                            type: boolean
                          hostPID:
                            type: boolean
                          hostUsers:
                            type: boolean
                          hostname:
                            type: string
                          imagePullSecrets:
                            items:
                              properties:
                                name:
                                  type: string
                              type: object
                            type: array
                          initContainers:
                            items:
                              properties:
                                args:
                                  items:
                                    type: string
                                  type: array
                                command:
                                  items:
                                    type: string
                                  type: array
                                env:
                                  items:
                                    properties:
                                      name:
                                        type: string
                                      value:
                                        type: string
                                      valueFrom:
                                        properties:
                                          configMapKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            required:
                                            - key
                                            type: object
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            required:
                                            - fieldPath
                                            type: object
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            required:
                                            - resource
                                            type: object
                                          secretKeyRef:
                                            properties:
                                              key:
                                                type: string
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            required:
                                            - key
                                            type: object
                                        type: object
                                    required:
                                    - name
                                    type: object
                                  type: array
                                envFrom:
                                  items:
                                    properties:
                                      configMapRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                      prefix:
                                        type: string
                                      secretRef:
                                        properties:
                                          name:
                                            type: string
                                          optional:
                                            type: boolean
                                        type: object
                                    type: object
                                  type: array
                                image:
                                  type: string
                                imagePullPolicy:
                                  type: string
                                lifecycle:
                                  properties:
                                    postStart:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                required:
                                                - name
                                                - value
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          required:
                                          - port
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              format: int64
                                              type: integer
                                          required:
                                          - seconds
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          required:
                                          - port
                                          type: object
                                      type: object
                                    preStop:
                                      properties:
                                        exec:
                                          properties:
                                            command:
                                              items:
                                                type: string
                                              type: array
                                          type: object
                                        httpGet:
                                          properties:
                                            host:
                                              type: string
                                            httpHeaders:
                                              items:
                                                properties:
                                                  name:
                                                    type: string
                                                  value:
                                                    type: string
                                                required:
                                                - name
                                                - value
                                                type: object
                                              type: array
                                            path:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                            scheme:
                                              type: string
                                          required:
                                          - port
                                          type: object
                                        sleep:
                                          properties:
                                            seconds:
                                              format: int64
                                              type: integer
                                          required:
                                          - seconds
                                          type: object
                                        tcpSocket:
                                          properties:
                                            host:
                                              type: string
                                            port:
                                              anyOf:
                                              - type: integer
                                              - type: string
                                              x-kubernetes-int-or-string: true
                                          required:
                                          - port
                                          type: object
                                      type: object
                                  type: object
                                livenessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                name:
                                  type: string
                                ports:
                                  items:
                                    properties:
                                      containerPort:
                                        format: int32
                                        type: integer
                                      hostIP:
                                        type: string
                                      hostPort:
                                        format: int32
                                        type: integer
                                      name:
                                        type: string
                                      protocol:
                                        type: string
                                    required:
                                    - containerPort
                                    type: object
                                  type: array
                                  x-kubernetes-list-map-keys:
                                  - containerPort
                                  - protocol
                                  x-kubernetes-list-type: map
                                readinessProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                resizePolicy:
                                  items:
                                    properties:
                                      resourceName:
                                        type: string
                                      restartPolicy:
                                        type: string
                                    required:
                                    - resourceName
                                    - restartPolicy
                                    type: object
                                  type: array
                                  x-kubernetes-list-type: atomic
                                resources:
                                  properties:
                                    claims:
                                      items:
                                        properties:
                                          name:
                                            type: string
                                        required:
                                        - name
                                        type: object
                                      type: array
                                      x-kubernetes-list-map-keys:
                                      - name
                                      x-kubernetes-list-type: map
                                    limits:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                    requests:
                                      additionalProperties:
                                        anyOf:
                                        - type: integer
                                        - type: string
                                        pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                        x-kubernetes-int-or-string: true
                                      type: object
                                  type: object
                                restartPolicy:
                                  type: string
                                securityContext:
                                  properties:
                                    allowPrivilegeEscalation:
                                      type: boolean
                                    capabilities:
                                      properties:
                                        add:
                                          items:
                                            type: string
                                          type: array
                                        drop:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    privileged:
                                      type: boolean
                                    procMount:
                                      type: string
                                    readOnlyRootFilesystem:
                                      type: boolean
                                    runAsGroup:
                                      format: int64
                                      type: integer
                                    runAsNonRoot:
                                      type: boolean
                                    runAsUser:
                                      format: int64
                                      type: integer
                                    seLinuxOptions:
                                      properties:
                                        level:
                                          type: string
                                        role:
                                          type: string
                                        type:
                                          type: string
                                        user:
                                          type: string
                                      type: object
                                    seccompProfile:
                                      properties:
                                        localhostProfile:
                                          type: string
                                        type:
                                          type: string
                                      required:
                                      - type
                                      type: object
                                    windowsOptions:
                                      properties:
                                        gmsaCredentialSpec:
                                          type: string
                                        gmsaCredentialSpecName:
                                          type: string
                                        hostProcess:
                                          type: boolean
                                        runAsUserName:
                                          type: string
                                      type: object
                                  type: object
                                startupProbe:
                                  properties:
                                    exec:
                                      properties:
                                        command:
                                          items:
                                            type: string
                                          type: array
                                      type: object
                                    failureThreshold:
                                      format: int32
                                      type: integer
                                    grpc:
                                      properties:
                                        port:
                                          format: int32
                                          type: integer
                                        service:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    httpGet:
                                      properties:
                                        host:
                                          type: string
                                        httpHeaders:
                                          items:
                                            properties:
                                              name:
                                                type: string
                                              value:
                                                type: string
                                            required:
                                            - name
                                            - value
                                            type: object
                                          type: array
                                        path:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                        scheme:
                                          type: string
                                      required:
                                      - port
                                      type: object
                                    initialDelaySeconds:
                                      format: int32
                                      type: integer
                                    periodSeconds:
                                      format: int32
                                      type: integer
                                    successThreshold:
                                      format: int32
                                      type: integer
                                    tcpSocket:
                                      properties:
                                        host:
                                          type: string
                                        port:
                                          anyOf:
                                          - type: integer
                                          - type: string
                                          x-kubernetes-int-or-string: true
                                      required:
                                      - port
                                      type: object
                                    terminationGracePeriodSeconds:
                                      format: int64
                                      type: integer
                                    timeoutSeconds:
                                      format: int32
                                      type: integer
                                  type: object
                                stdin:
                                  type: boolean
                                stdinOnce:
                                  type: boolean
                                terminationMessagePath:
                                  type: string
                                terminationMessagePolicy:
                                  type: string
                                tty:
                                  type: boolean
                                volumeDevices:
                                  items:
                                    properties:
                                      devicePath:
                                        type: string
                                      name:
                                        type: string
                                    required:
                                    - devicePath
                                    - name
                                    type: object
                                  type: array
                                volumeMounts:
                                  items:
                                    properties:
                                      mountPath:
                                        type: string
                                      mountPropagation:
                                        type: string
                                      name:
                                        type: string
                                      readOnly:
                                        type: boolean
                                      subPath:
                                        type: string
                                      subPathExpr:
                                        type: string
                                    required:
                                    - mountPath
                                    - name
                                    type: object
                                  type: array
                                workingDir:
                                  type: string
                              required:
                              - name
                              type: object
                            type: array
                          nodeName:
                            type: string
                          nodeSelector:
                            additionalProperties:
                              type: string
                            type: object
                            x-kubernetes-map-type: atomic
                          os:
                            properties:
                              name:
                                type: string
                            required:
                            - name
                            type: object
                          overhead:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            type: object
                          preemptionPolicy:
                            type: string
                          priority:
                            format: int32
                            type: integer
                          priorityClassName:
                            type: string
                          readinessGates:
                            items:
                              properties:
                                conditionType:
                                  type: string
                              required:
                              - conditionType
                              type: object
                            type: array
                          resourceClaims:
                            items:
                              properties:
                                name:
                                  type: string
                                source:
                                  properties:
                                    resourceClaimName:
                                      type: string
                                    resourceClaimTemplateName:
                                      type: string
                                  type: object
                              required:
                              - name
                              type: object
                            type: array
                            x-kubernetes-list-map-keys:
                            - name
                            x-kubernetes-list-type: map
                          restartPolicy:
                            type: string
                          runtimeClassName:
                            type: string
                          schedulerName:
                            type: string
                          schedulingGates:
                            items:
                              properties:
                                name:
                                  type: string
                              required:
                              - name
                              type: object
                            type: array
                            x-kubernetes-list-map-keys:
                            - name
                            x-kubernetes-list-type: map
                          securityContext:
                            properties:
                              fsGroup:
                                format: int64
                                type: integer
                              fsGroupChangePolicy:
                                type: string
                              runAsGroup:
                                format: int64
                                type: integer
                              runAsNonRoot:
                                type: boolean
                              runAsUser:
                                format: int64
                                type: integer
                              seLinuxOptions:
                                properties:
                                  level:
                                    type: string
                                  role:
                                    type: string
                                  type:
                                    type: string
                                  user:
                                    type: string
                                type: object
                              seccompProfile:
                                properties:
                                  localhostProfile:
                                    type: string
                                  type:
                                    type: string
                                required:
                                - type
                                type: object
                              supplementalGroups:
                                items:
                                  format: int64
                                  type: integer
                                type: array
                              sysctls:
                                items:
                                  properties:
                                    name:
                                      type: string
                                    value:
                                      type: string
                                  required:
                                  - name
                                  - value
                                  type: object
                                type: array
                              windowsOptions:
                                properties:
                                  gmsaCredentialSpec:
                                    type: string
                                  gmsaCredentialSpecName:
                                    type: string
                                  hostProcess:
                                    type: boolean
                                  runAsUserName:
                                    type: string
                                type: object
                            type: object
                          serviceAccount:
                            type: string
                          serviceAccountName:
                            type: string
                          setHostnameAsFQDN:
                            type: boolean
                          shareProcessNamespace:
                            type: boolean
                          subdomain:
                            type: string
                          terminationGracePeriodSeconds:
                            format: int64
                            type: integer
                          tolerations:
                            items:
                              properties:
                                effect:
                                  type: string
                                key:
                                  type: string
                                operator:
                                  type: string
                                tolerationSeconds:
                                  format: int64
                                  type: integer
                                value:
                                  type: string
                              type: object
                            type: array
                          topologySpreadConstraints:
                            items:
                              properties:
                                labelSelector:
                                  properties:
                                    matchExpressions:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          operator:
                                            type: string
                                          values:
                                            items:
                                              type: string
                                            type: array
                                        required:
                                        - key
                                        - operator
                                        type: object
                                      type: array
                                    matchLabels:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  type: object
                                matchLabelKeys:
                                  items:
                                    type: string
                                  type: array
                                  x-kubernetes-list-type: atomic
                                maxSkew:
                                  format: int32
                                  type: integer
                                minDomains:
                                  format: int32
                                  type: integer
                                nodeAffinityPolicy:
                                  type: string
                                nodeTaintsPolicy:
                                  type: string
                                topologyKey:
                                  type: string
                                whenUnsatisfiable:
                                  type: string
                              required:
                              - maxSkew
                              - topologyKey
                              - whenUnsatisfiable
                              type: object
                            type: array
                            x-kubernetes-list-map-keys:
                            - topologyKey
                            - whenUnsatisfiable
                            x-kubernetes-list-type: map
                          volumes:
                            items:
                              properties:
                                awsElasticBlockStore:
                                  properties:
                                    fsType:
                                      type: string
                                    partition:
                                      format: int32
                                      type: integer
                                    readOnly:
                                      type: boolean
                                    volumeID:
                                      type: string
                                  required:
                                  - volumeID
                                  type: object
                                azureDisk:
                                  properties:
                                    cachingMode:
                                      type: string
                                    diskName:
                                      type: string
                                    diskURI:
                                      type: string
                                    fsType:
                                      type: string
                                    kind:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  required:
                                  - diskName
                                  - diskURI
                                  type: object
                                azureFile:
                                  properties:
                                    readOnly:
                                      type: boolean
                                    secretName:
                                      type: string
                                    shareName:
                                      type: string
                                  required:
                                  - secretName
                                  - shareName
                                  type: object
                                cephfs:
                                  properties:
                                    monitors:
                                      items:
                                        type: string
                                      type: array
                                    path:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretFile:
                                      type: string
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    user:
                                      type: string
                                  required:
                                  - monitors
                                  type: object
                                cinder:
                                  properties:
                                    fsType:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    volumeID:
                                      type: string
                                  required:
                                  - volumeID
                                  type: object
                                configMap:
                                  properties:
                                    defaultMode:
                                      format: int32
                                      type: integer
                                    items:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          mode:
                                            format: int32
                                            type: integer
                                          path:
                                            type: string
                                        required:
                                        - key
                                        - path
                                        type: object
                                      type: array
                                    name:
                                      type: string
                                    optional:
                                      type: boolean
                                  type: object
                                csi:
                                  properties:
                                    driver:
                                      type: string
                                    fsType:
                                      type: string
                                    nodePublishSecretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    readOnly:
                                      type: boolean
                                    volumeAttributes:
                                      additionalProperties:
                                        type: string
                                      type: object
                                  required:
                                  - driver
                                  type: object
                                downwardAPI:
                                  properties:
                                    defaultMode:
                                      format: int32
                                      type: integer
                                    items:
                                      items:
                                        properties:
                                          fieldRef:
                                            properties:
                                              apiVersion:
                                                type: string
                                              fieldPath:
                                                type: string
                                            required:
                                            - fieldPath
                                            type: object
                                          mode:
                                            format: int32
                                            type: integer
                                          path:
                                            type: string
                                          resourceFieldRef:
                                            properties:
                                              containerName:
                                                type: string
                                              divisor:
                                                anyOf:
                                                - type: integer
                                                - type: string
                                                pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                x-kubernetes-int-or-string: true
                                              resource:
                                                type: string
                                            required:
                                            - resource
                                            type: object
                                        required:
                                        - path
                                        type: object
                                      type: array
                                  type: object
                                emptyDir:
                                  properties:
                                    medium:
                                      type: string
                                    sizeLimit:
                                      anyOf:
                                      - type: integer
                                      - type: string
                                      pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                      x-kubernetes-int-or-string: true
                                  type: object
                                ephemeral:
                                  properties:
                                    volumeClaimTemplate:
                                      properties:
                                        metadata:
                                          type: object
                                        spec:
                                          properties:
                                            accessModes:
                                              items:
                                                type: string
                                              type: array
                                            dataSource:
                                              properties:
                                                apiGroup:
                                                  type: string
                                                kind:
                                                  type: string
                                                name:
                                                  type: string
                                              required:
                                              - kind
                                              - name
                                              type: object
                                            dataSourceRef:
                                              properties:
                                                apiGroup:
                                                  type: string
                                                kind:
                                                  type: string
                                                name:
                                                  type: string
                                                namespace:
                                                  type: string
                                              required:
                                              - kind
                                              - name
                                              type: object
                                            resources:
                                              properties:
                                                limits:
                                                  additionalProperties:
                                                    anyOf:
                                                    - type: integer
                                                    - type: string
                                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                    x-kubernetes-int-or-string: true
                                                  type: object
                                                requests:
                                                  additionalProperties:
                                                    anyOf:
                                                    - type: integer
                                                    - type: string
                                                    pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                    x-kubernetes-int-or-string: true
                                                  type: object
                                              type: object
                                            selector:
                                              properties:
                                                matchExpressions:
                                                  items:
                                                    properties:
                                                      key:
                                                        type: string
                                                      operator:
                                                        type: string
                                                      values:
                                                        items:
                                                          type: string
                                                        type: array
                                                    required:
                                                    - key
                                                    - operator
                                                    type: object
                                                  type: array
                                                matchLabels:
                                                  additionalProperties:
                                                    type: string
                                                  type: object
                                              type: object
                                            storageClassName:
                                              type: string
                                            volumeAttributesClassName:
                                              type: string
                                            volumeMode:
                                              type: string
                                            volumeName:
                                              type: string
                                          type: object
                                      required:
                                      - spec
                                      type: object
                                  type: object
                                fc:
                                  properties:
                                    fsType:
                                      type: string
                                    lun:
                                      format: int32
                                      type: integer
                                    readOnly:
                                      type: boolean
                                    targetWWNs:
                                      items:
                                        type: string
                                      type: array
                                    wwids:
                                      items:
                                        type: string
                                      type: array
                                  type: object
                                flexVolume:
                                  properties:
                                    driver:
                                      type: string
                                    fsType:
                                      type: string
                                    options:
                                      additionalProperties:
                                        type: string
                                      type: object
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                  required:
                                  - driver
                                  type: object
                                flocker:
                                  properties:
                                    datasetName:
                                      type: string
                                    datasetUUID:
                                      type: string
                                  type: object
                                gcePersistentDisk:
                                  properties:
                                    fsType:
                                      type: string
                                    partition:
                                      format: int32
                                      type: integer
                                    pdName:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  required:
                                  - pdName
                                  type: object
                                gitRepo:
                                  properties:
                                    directory:
                                      type: string
                                    repository:
                                      type: string
                                    revision:
                                      type: string
                                  required:
                                  - repository
                                  type: object
                                glusterfs:
                                  properties:
                                    endpoints:
                                      type: string
                                    path:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  required:
                                  - endpoints
                                  - path
                                  type: object
                                hostPath:
                                  properties:
                                    path:
                                      type: string
                                    type:
                                      type: string
                                  required:
                                  - path
                                  type: object
                                iscsi:
                                  properties:
                                    chapAuthDiscovery:
                                      type: boolean
                                    chapAuthSession:
                                      type: boolean
                                    fsType:
                                      type: string
                                    initiatorName:
                                      type: string
                                    iqn:
                                      type: string
                                    iscsiInterface:
                                      type: string
                                    lun:
                                      format: int32
                                      type: integer
                                    portals:
                                      items:
                                        type: string
                                      type: array
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    targetPortal:
                                      type: string
                                  required:
                                  - iqn
                                  - lun
                                  - targetPortal
                                  type: object
                                name:
                                  type: string
                                nfs:
                                  properties:
                                    path:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    server:
                                      type: string
                                  required:
                                  - path
                                  - server
                                  type: object
                                persistentVolumeClaim:
                                  properties:
                                    claimName:
                                      type: string
                                    readOnly:
                                      type: boolean
                                  required:
                                  - claimName
                                  type: object
                                photonPersistentDisk:
                                  properties:
                                    fsType:
                                      type: string
                                    pdID:
                                      type: string
                                  required:
                                  - pdID
                                  type: object
                                portworxVolume:
                                  properties:
                                    fsType:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    volumeID:
                                      type: string
                                  required:
                                  - volumeID
                                  type: object
                                projected:
                                  properties:
                                    defaultMode:
                                      format: int32
                                      type: integer
                                    sources:
                                      items:
                                        properties:
                                          clusterTrustBundle:
                                            properties:
                                              labelSelector:
                                                properties:
                                                  matchExpressions:
                                                    items:
                                                      properties:
                                                        key:
                                                          type: string
                                                        operator:
                                                          type: string
                                                        values:
                                                          items:
                                                            type: string
                                                          type: array
                                                      required:
                                                      - key
                                                      - operator
                                                      type: object
                                                    type: array
                                                  matchLabels:
                                                    additionalProperties:
                                                      type: string
                                                    type: object
                                                type: object
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                              path:
                                                type: string
                                              signerName:
                                                type: string
                                            required:
                                            - path
                                            type: object
                                          configMap:
                                            properties:
                                              items:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    mode:
                                                      format: int32
                                                      type: integer
                                                    path:
                                                      type: string
                                                  required:
                                                  - key
                                                  - path
                                                  type: object
                                                type: array
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                          downwardAPI:
                                            properties:
                                              items:
                                                items:
                                                  properties:
                                                    fieldRef:
                                                      properties:
                                                        apiVersion:
                                                          type: string
                                                        fieldPath:
                                                          type: string
                                                      required:
                                                      - fieldPath
                                                      type: object
                                                    mode:
                                                      format: int32
                                                      type: integer
                                                    path:
                                                      type: string
                                                    resourceFieldRef:
                                                      properties:
                                                        containerName:
                                                          type: string
                                                        divisor:
                                                          anyOf:
                                                          - type: integer
                                                          - type: string
                                                          pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                                                          x-kubernetes-int-or-string: true
                                                        resource:
                                                          type: string
                                                      required:
                                                      - resource
                                                      type: object
                                                  required:
                                                  - path
                                                  type: object
                                                type: array
                                            type: object
                                          secret:
                                            properties:
                                              items:
                                                items:
                                                  properties:
                                                    key:
                                                      type: string
                                                    mode:
                                                      format: int32
                                                      type: integer
                                                    path:
                                                      type: string
                                                  required:
                                                  - key
                                                  - path
                                                  type: object
                                                type: array
                                              name:
                                                type: string
                                              optional:
                                                type: boolean
                                            type: object
                                          serviceAccountToken:
                                            properties:
                                              audience:
                                                type: string
                                              expirationSeconds:
                                                format: int64
                                                type: integer
                                              path:
                                                type: string
                                            required:
                                            - path
                                            type: object
                                        type: object
                                      type: array
                                  type: object
                                quobyte:
                                  properties:
                                    group:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    registry:
                                      type: string
                                    tenant:
                                      type: string
                                    user:
                                      type: string
                                    volume:
                                      type: string
                                  required:
                                  - registry
                                  - volume
                                  type: object
                                rbd:
                                  properties:
                                    fsType:
                                      type: string
                                    image:
                                      type: string
                                    keyring:
                                      type: string
                                    monitors:
                                      items:
                                        type: string
                                      type: array
                                    pool:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    user:
                                      type: string
                                  required:
                                  - image
                                  - monitors
                                  type: object
                                scaleIO:
                                  properties:
                                    fsType:
                                      type: string
                                    gateway:
                                      type: string
                                    protectionDomain:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    sslEnabled:
                                      type: boolean
                                    storageMode:
                                      type: string
                                    storagePool:
                                      type: string
                                    system:
                                      type: string
                                    volumeName:
                                      type: string
                                  required:
                                  - gateway
                                  - secretRef
                                  - system
                                  type: object
                                secret:
                                  properties:
                                    defaultMode:
                                      format: int32
                                      type: integer
                                    items:
                                      items:
                                        properties:
                                          key:
                                            type: string
                                          mode:
                                            format: int32
                                            type: integer
                                          path:
                                            type: string
                                        required:
                                        - key
                                        - path
                                        type: object
                                      type: array
                                    optional:
                                      type: boolean
                                    secretName:
                                      type: string
                                  type: object
                                storageos:
                                  properties:
                                    fsType:
                                      type: string
                                    readOnly:
                                      type: boolean
                                    secretRef:
                                      properties:
                                        name:
                                          type: string
                                      type: object
                                    volumeName:
                                      type: string
                                    volumeNamespace:
                                      type: string
                                  type: object
                                vsphereVolume:
                                  properties:
                                    fsType:
                                      type: string
                                    storagePolicyID:
                                      type: string
                                    storagePolicyName:
                                      type: string
                                    volumePath:
                                      type: string
                                  required:
                                  - volumePath
                                  type: object
                              required:
                              - name
                              type: object
                            type: array
                        required:
                        - containers
                        type: object
                    type: object
                  topologyPolicy:
                    type: string
                type: object
              type: array
            ttlSecondsAfterFinished:
              format: int32
              type: integer
            volumes:
              items:
                properties:
                  mountPath:
                    type: string
                  volumeClaim:
                    properties:
                      accessModes:
                        items:
                          type: string
                        type: array
                      dataSource:
                        properties:
                          apiGroup:
                            type: string
                          kind:
                            type: string
                          name:
                            type: string
                        required:
                        - kind
                        - name
                        type: object
                      dataSourceRef:
                        properties:
                          apiGroup:
                            type: string
                          kind:
                            type: string
                          name:
                            type: string
                          namespace:
                            type: string
                        required:
                        - kind
                        - name
                        type: object
                      resources:
                        properties:
                          limits:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            type: object
                          requests:
                            additionalProperties:
                              anyOf:
                              - type: integer
                              - type: string
                              pattern: ^(\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\+|-)?(([0-9]+(\.[0-9]*)?)|(\.[0-9]+))))?$
                              x-kubernetes-int-or-string: true
                            type: object
                        type: object
                      selector:
                        properties:
                          matchExpressions:
                            items:
                              properties:
                                key:
                                  type: string
                                operator:
                                  type: string
                                values:
                                  items:
                                    type: string
                                  type: array
                              required:
                              - key
                              - operator
                              type: object
                            type: array
                          matchLabels:
                            additionalProperties:
                              type: string
                            type: object
                        type: object
                      storageClassName:
                        type: string
                      volumeAttributesClassName:
                        type: string
                      volumeMode:
                        type: string
                      volumeName:
                        type: string
                    type: object
                  volumeClaimName:
                    type: string
                required:
                - mountPath
                type: object
              type: array
          type: object
        status:
          properties:
            conditions:
              items:
                properties:
                  lastTransitionTime:
                    format: date-time
                    type: string
                  status:
                    type: string
                required:
                - status
                type: object
              type: array
            controlledResources:
              additionalProperties:
                type: string
              type: object
            failed:
              format: int32
              type: integer
            minAvailable:
              format: int32
              type: integer
            pending:
              format: int32
              type: integer
            retryCount:
              format: int32
              type: integer
            running:
              format: int32
              type: integer
            runningDuration:
              type: string
            state:
              properties:
                lastTransitionTime:
                  format: date-time
                  type: string
                message:
                  type: string
                phase:
                  type: string
                reason:
                  type: string
              type: object
            succeeded:
              format: int32
              type: integer
            taskStatusCount:
              additionalProperties:
                properties:
                  phase:
                    additionalProperties:
                      format: int32
                      type: integer
                    type: object
                type: object
              type: object
            terminating:
              format: int32
              type: integer
            unknown:
              format: int32
              type: integer
            version:
              format: int32
              type: integer
          type: object
      type: object
  version: v1alpha1
  versions:
  - name: v1alpha1
    served: true
    storage: true
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
