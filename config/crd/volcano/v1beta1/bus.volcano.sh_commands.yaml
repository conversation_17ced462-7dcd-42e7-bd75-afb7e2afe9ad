
---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.6.0
  creationTimestamp: null
  name: commands.bus.volcano.sh
spec:
  group: bus.volcano.sh
  names:
    kind: Command
    listKind: CommandList
    plural: commands
    singular: command
  scope: Namespaced
  validation:
    openAPIV3Schema:
      description: Command defines command structure.
      properties:
        action:
          description: Action defines the action that will be took to the target object.
          type: string
        apiVersion:
          description: 'APIVersion defines the versioned schema of this representation
            of an object. Servers should convert recognized schemas to the latest
            internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
          type: string
        kind:
          description: 'Kind is a string value representing the REST resource this
            object represents. Servers may infer this from the endpoint the client
            submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
          type: string
        message:
          description: Human-readable message indicating details of this command.
          type: string
        metadata:
          type: object
        reason:
          description: Unique, one-word, CamelCase reason for this command.
          type: string
        target:
          description: TargetObject defines the target object of this command.
          properties:
            apiVersion:
              description: API version of the referent.
              type: string
            blockOwnerDeletion:
              description: If true, AND if the owner has the "foregroundDeletion"
                finalizer, then the owner cannot be deleted from the key-value store
                until this reference is removed. See https://kubernetes.io/docs/concepts/architecture/garbage-collection/#foreground-deletion
                for how the garbage collector interacts with this field and enforces
                the foreground deletion. Defaults to false. To set this field, a user
                needs "delete" permission of the owner, otherwise 422 (Unprocessable
                Entity) will be returned.
              type: boolean
            controller:
              description: If true, this reference points to the managing controller.
              type: boolean
            kind:
              description: 'Kind of the referent. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
              type: string
            name:
              description: 'Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#names'
              type: string
            uid:
              description: 'UID of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names#uids'
              type: string
          required:
          - apiVersion
          - kind
          - name
          - uid
          type: object
      type: object
  version: v1alpha1
  versions:
  - name: v1alpha1
    served: true
    storage: true
status:
  acceptedNames:
    kind: ""
    plural: ""
  conditions: []
  storedVersions: []
