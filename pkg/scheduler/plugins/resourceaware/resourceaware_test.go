/*
Copyright 2024 The Volcano Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package resourceaware

import (
	"testing"

	"volcano.sh/volcano/pkg/scheduler/api"
	"volcano.sh/volcano/pkg/scheduler/framework"
)

func TestResourceAwarePlugin(t *testing.T) {
	// Create test framework
	framework.RegisterPluginBuilder(PluginName, New)
	defer framework.CleanupPluginBuilders()

	// Test cases
	tests := []struct {
		name           string
		arguments      framework.Arguments
		expectedCPU    float64
		expectedMemory float64
		expectedWeight float64
	}{
		{
			name:           "default configuration",
			arguments:      framework.Arguments{},
			expectedCPU:    0.8,
			expectedMemory: 0.8,
			expectedWeight: 1.0,
		},
		{
			name: "custom configuration",
			arguments: framework.Arguments{
				"cpu.threshold":    "0.7",
				"memory.threshold": "0.9",
				"weight":           "2.0",
			},
			expectedCPU:    0.7,
			expectedMemory: 0.9,
			expectedWeight: 2.0,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			plugin := New(test.arguments).(*resourceAwarePlugin)

			if plugin.cpuThreshold != test.expectedCPU {
				t.Errorf("Expected CPU threshold %f, got %f", test.expectedCPU, plugin.cpuThreshold)
			}
			if plugin.memoryThreshold != test.expectedMemory {
				t.Errorf("Expected memory threshold %f, got %f", test.expectedMemory, plugin.memoryThreshold)
			}
			if plugin.weight != test.expectedWeight {
				t.Errorf("Expected weight %f, got %f", test.expectedWeight, plugin.weight)
			}
		})
	}
}

func TestNodeUtilization(t *testing.T) {
	plugin := New(framework.Arguments{}).(*resourceAwarePlugin)

	// Create test node
	node := &api.NodeInfo{
		Name: "test-node",
		Allocatable: &api.Resource{
			MilliCPU: 4000,                   // 4 CPUs
			Memory:   8 * 1024 * 1024 * 1024, // 8GB
		},
		Idle: &api.Resource{
			MilliCPU: 1000,                   // 1 CPU idle
			Memory:   2 * 1024 * 1024 * 1024, // 2GB idle
		},
	}

	// Test CPU utilization calculation
	expectedCPUUtil := 0.75 // (4000-1000)/4000 = 0.75
	actualCPUUtil := plugin.getCPUUtilization(node)
	if actualCPUUtil != expectedCPUUtil {
		t.Errorf("Expected CPU utilization %f, got %f", expectedCPUUtil, actualCPUUtil)
	}

	// Test memory utilization calculation
	expectedMemUtil := 0.75 // (8GB-2GB)/8GB = 0.75
	actualMemUtil := plugin.getMemoryUtilization(node)
	if actualMemUtil != expectedMemUtil {
		t.Errorf("Expected memory utilization %f, got %f", expectedMemUtil, actualMemUtil)
	}

	// Test node overload detection
	if plugin.isNodeOverloaded(node) {
		t.Error("Node should not be considered overloaded with 75% utilization and 80% threshold")
	}

	// Test with higher utilization
	node.Idle.MilliCPU = 500              // 87.5% CPU utilization
	node.Idle.Memory = 1024 * 1024 * 1024 // 87.5% memory utilization

	if !plugin.isNodeOverloaded(node) {
		t.Error("Node should be considered overloaded with 87.5% utilization and 80% threshold")
	}
}

func TestNodeScoring(t *testing.T) {
	plugin := New(framework.Arguments{}).(*resourceAwarePlugin)

	// Create test task
	task := &api.TaskInfo{
		UID:       "test-task",
		Name:      "test-task",
		Namespace: "default",
	}

	// Create test nodes with different utilization levels
	lowUtilNode := &api.NodeInfo{
		Name: "low-util-node",
		Allocatable: &api.Resource{
			MilliCPU: 4000,
			Memory:   8 * 1024 * 1024 * 1024,
		},
		Idle: &api.Resource{
			MilliCPU: 3000,                   // 25% utilization
			Memory:   6 * 1024 * 1024 * 1024, // 25% utilization
		},
	}

	highUtilNode := &api.NodeInfo{
		Name: "high-util-node",
		Allocatable: &api.Resource{
			MilliCPU: 4000,
			Memory:   8 * 1024 * 1024 * 1024,
		},
		Idle: &api.Resource{
			MilliCPU: 1000,                   // 75% utilization
			Memory:   2 * 1024 * 1024 * 1024, // 75% utilization
		},
	}

	lowUtilScore := plugin.calculateNodeScore(task, lowUtilNode)
	highUtilScore := plugin.calculateNodeScore(task, highUtilNode)

	// Low utilization node should have higher score
	if lowUtilScore <= highUtilScore {
		t.Errorf("Low utilization node should have higher score. Low: %f, High: %f",
			lowUtilScore, highUtilScore)
	}
}

func TestPluginName(t *testing.T) {
	plugin := New(framework.Arguments{})
	if plugin.Name() != PluginName {
		t.Errorf("Expected plugin name %s, got %s", PluginName, plugin.Name())
	}
}
