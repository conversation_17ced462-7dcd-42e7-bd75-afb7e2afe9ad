/*
Copyright 2024 The Volcano Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package resourceaware

import (
	"fmt"
	"strconv"

	"k8s.io/klog/v2"

	"volcano.sh/volcano/pkg/scheduler/api"
	"volcano.sh/volcano/pkg/scheduler/framework"
	"volcano.sh/volcano/pkg/scheduler/plugins/util"
)

// PluginName indicates name of volcano scheduler plugin.
const PluginName = "resourceaware"

// resourceAwarePlugin is a plugin that considers resource utilization when making scheduling decisions
type resourceAwarePlugin struct {
	// Arguments given for the plugin
	pluginArguments framework.Arguments

	// Configuration parameters
	cpuThreshold    float64 // CPU utilization threshold (0.0-1.0)
	memoryThreshold float64 // Memory utilization threshold (0.0-1.0)
	weight          float64 // Weight for node scoring
}

// New creates a new resourceaware plugin
func New(arguments framework.Arguments) framework.Plugin {
	plugin := &resourceAwarePlugin{
		pluginArguments: arguments,
		cpuThreshold:    0.8, // Default 80% CPU threshold
		memoryThreshold: 0.8, // Default 80% memory threshold
		weight:          1.0, // Default weight
	}

	// Parse plugin arguments
	plugin.parseArguments()

	return plugin
}

// Name returns the plugin name
func (rap *resourceAwarePlugin) Name() string {
	return PluginName
}

// parseArguments parses plugin configuration arguments
func (rap *resourceAwarePlugin) parseArguments() {
	for key, value := range rap.pluginArguments {
		valueStr, ok := value.(string)
		if !ok {
			continue
		}
		switch key {
		case "cpu.threshold":
			if threshold, err := strconv.ParseFloat(valueStr, 64); err == nil {
				if threshold > 0 && threshold <= 1.0 {
					rap.cpuThreshold = threshold
				}
			}
		case "memory.threshold":
			if threshold, err := strconv.ParseFloat(valueStr, 64); err == nil {
				if threshold > 0 && threshold <= 1.0 {
					rap.memoryThreshold = threshold
				}
			}
		case "weight":
			if weight, err := strconv.ParseFloat(valueStr, 64); err == nil {
				if weight > 0 {
					rap.weight = weight
				}
			}
		}
	}

	klog.V(4).Infof("ResourceAware plugin initialized with CPU threshold: %.2f, Memory threshold: %.2f, Weight: %.2f",
		rap.cpuThreshold, rap.memoryThreshold, rap.weight)
}

// OnSessionOpen is called when a new scheduling session starts
func (rap *resourceAwarePlugin) OnSessionOpen(ssn *framework.Session) {
	klog.V(4).Infof("Enter ResourceAware plugin ...")

	// Register predicate function to filter out overloaded nodes
	predicateFn := func(task *api.TaskInfo, node *api.NodeInfo) error {
		// Check if node has enough resources considering thresholds
		if rap.isNodeOverloaded(node) {
			return fmt.Errorf("node %s is overloaded (CPU: %.2f%%, Memory: %.2f%%)",
				node.Name, rap.getCPUUtilization(node)*100, rap.getMemoryUtilization(node)*100)
		}
		return nil
	}
	ssn.AddPredicateFn(rap.Name(), predicateFn)

	// Register node order function to score nodes based on resource utilization
	nodeOrderFn := func(task *api.TaskInfo, node *api.NodeInfo) (float64, error) {
		score := rap.calculateNodeScore(task, node)
		klog.V(5).Infof("Node %s score for task %s/%s: %.2f",
			node.Name, task.Namespace, task.Name, score)
		return score, nil
	}
	ssn.AddNodeOrderFn(rap.Name(), nodeOrderFn)

	// Register allocatable function to check if queue can allocate task
	allocatableFn := func(queue *api.QueueInfo, candidate *api.TaskInfo) bool {
		// Allow allocation if the task is not too resource-intensive
		// This is a simple check - in practice, you might want more sophisticated logic
		return true
	}
	ssn.AddAllocatableFn(rap.Name(), allocatableFn)

	// Register preemptable function to determine which tasks can be preempted
	preemptableFn := func(preemptor *api.TaskInfo, preemptees []*api.TaskInfo) ([]*api.TaskInfo, int) {
		var victims []*api.TaskInfo

		// Only allow preemption of lower priority tasks
		for _, preemptee := range preemptees {
			if preemptee.Priority < preemptor.Priority {
				victims = append(victims, preemptee)
			}
		}

		klog.V(4).Infof("ResourceAware plugin found %d preemptable tasks for preemptor %s/%s",
			len(victims), preemptor.Namespace, preemptor.Name)

		return victims, util.Permit
	}
	ssn.AddPreemptableFn(rap.Name(), preemptableFn)
	ssn.AddReclaimableFn(rap.Name(), preemptableFn)
}

// OnSessionClose is called when a scheduling session ends
func (rap *resourceAwarePlugin) OnSessionClose(ssn *framework.Session) {
	klog.V(4).Infof("Leaving ResourceAware plugin ...")
}

// isNodeOverloaded checks if a node is overloaded based on resource thresholds
func (rap *resourceAwarePlugin) isNodeOverloaded(node *api.NodeInfo) bool {
	cpuUtil := rap.getCPUUtilization(node)
	memUtil := rap.getMemoryUtilization(node)

	return cpuUtil > rap.cpuThreshold || memUtil > rap.memoryThreshold
}

// getCPUUtilization calculates CPU utilization percentage (0.0-1.0)
func (rap *resourceAwarePlugin) getCPUUtilization(node *api.NodeInfo) float64 {
	if node.Allocatable.MilliCPU == 0 {
		return 0.0
	}

	used := node.Allocatable.MilliCPU - node.Idle.MilliCPU
	return float64(used) / float64(node.Allocatable.MilliCPU)
}

// getMemoryUtilization calculates memory utilization percentage (0.0-1.0)
func (rap *resourceAwarePlugin) getMemoryUtilization(node *api.NodeInfo) float64 {
	if node.Allocatable.Memory == 0 {
		return 0.0
	}

	used := node.Allocatable.Memory - node.Idle.Memory
	return float64(used) / float64(node.Allocatable.Memory)
}

// calculateNodeScore calculates a score for the node based on resource utilization
// Higher score means better node (lower utilization)
func (rap *resourceAwarePlugin) calculateNodeScore(task *api.TaskInfo, node *api.NodeInfo) float64 {
	cpuUtil := rap.getCPUUtilization(node)
	memUtil := rap.getMemoryUtilization(node)

	// Calculate average utilization
	avgUtil := (cpuUtil + memUtil) / 2.0

	// Score is inversely proportional to utilization
	// Nodes with lower utilization get higher scores
	score := (1.0 - avgUtil) * 100.0 * rap.weight

	// Ensure score is non-negative
	if score < 0 {
		score = 0
	}

	return score
}
